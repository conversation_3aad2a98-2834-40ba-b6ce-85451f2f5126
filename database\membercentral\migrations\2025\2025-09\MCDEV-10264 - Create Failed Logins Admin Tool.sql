USE memberCentral
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @toolTypeID INT, @parentNavigationID INT, @navigationID INT, @RTID int, @RTFID int;

	-- Use existing DesktopAdmin tool type
	SELECT @toolTypeID = toolTypeID, @RTID = resourceTypeID
	FROM dbo.admin_toolTypes
	WHERE toolType = 'DesktopAdmin';

	-- Find Performance/Tasks parent navigation
	SELECT @parentNavigationID = n.navigationID
	FROM dbo.admin_navigation AS n
	INNER JOIN dbo.admin_navigation AS p ON p.navigationID = n.parentNavigationID
		AND p.navName = 'Admin'
		AND p.navAreaID = 1
	WHERE n.navName = 'Performance/Tasks'
	AND n.navAreaID = 2;

	-- Get resource type function ID for ViewDesktopAdminTool
	SELECT @RTFID=dbo.fn_getResourceTypeFunctionID(@RTID,dbo.fn_getResourceFunctionID('ViewDesktopAdminTool',@RTID));

	BEGIN TRAN;
		-- Create admin navigation for Failed Logins
		EXEC dbo.createAdminNavigation 
			@navName='Failed Logins', 
			@navDesc='Failed login attempts analysis and security monitoring', 
			@parentNavigationID=@parentNavigationID, 
			@navAreaID=3, 
			@cfcMethod='listFailedLogins', 
			@isHeader=0, 
			@showInNav=1, 
			@helpLink='', 
			@iconClasses='fa-light fa-shield-exclamation', 
			@navigationID=@navigationID OUTPUT;

		-- Link navigation to DesktopAdmin tool type
		EXEC dbo.createAdminFunctionsDeterminingNav 
			@resourceTypeFunctionID=@RTFID, 
			@toolTypeID=@toolTypeID, 
			@navigationID=@navigationID;

		-- Restrict to MC site only
		INSERT INTO dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		VALUES(@toolTypeID, 1);
	COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

-- Create admin suite for MC site only
DECLARE @siteID INT;
SELECT @siteID = dbo.fn_getSiteIDFromSiteCode('MC');
EXEC dbo.createAdminSuite @siteID=@siteID;
GO
