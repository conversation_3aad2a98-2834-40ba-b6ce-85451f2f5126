<cfoutput>
	<cfsavecontent variable="local.js">
	<script type="text/javascript">
		$(document).ready(function() {
			// Initialize date pickers
			mca_setupDatePickerRangeFields('dateFrom','dateTo');
			mca_setupCalendarIcons('frmFailedLoginsFilter');

			// Initialize all data tables
			initializeFailedLoginsTables();
		});

		var failedLoginsTables = {};

		function initializeFailedLoginsTables() {
			var baseURL = '<cfoutput>#local.filterFailedLoginsLink#</cfoutput>';
			var commonConfig = {
				"processing": true,
				"serverSide": false,
				"pageLength": 10,
				"lengthMenu": [5, 10, 25, 50],
				"searching": false,
				"ordering": false,
				"info": false,
				"autoWidth": false,
				"responsive": true,
				"dom": "<'row'<'col-sm-6'l><'col-sm-6'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12'p>>"
			};

			// Top Usernames Table
			failedLoginsTables.topUsernames = $('##tblTopUsernames').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'topUsernames';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Username", 'className': 'small'},
					{"data": 1, "title": "Failed Attempts", "className": "small text-right"}
				]
			}));

			// Top Passwords Table
			failedLoginsTables.topPasswords = $('##tblTopPasswords').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'topPasswords';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Password", 'className': 'small'},
					{"data": 1, "title": "Unique Usernames", "className": "small text-right"}
				]
			}));

			// Attempts Over Time Table
			failedLoginsTables.attemptsOverTime = $('##tblAttemptsOverTime').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'attemptsOverTime';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Date", 'className': 'small'},
					{"data": 1, "title": "Hour", 'className': 'small'},
					{"data": 2, "title": "Total Attempts", "className": "small text-right"}
				]
			}));

			// Failed By Site Table
			failedLoginsTables.failedBySite = $('##tblFailedBySite').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'failedBySite';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Site Code", 'className': 'small'},
					{"data": 1, "title": "Failed Attempts", "className": "small text-right"}
				]
			}));

			// Unique Combinations Table
			failedLoginsTables.uniqueCombinations = $('##tblUniqueCombinations').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'uniqueCombinations';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Total Failed Attempts", "className": "small text-right"},
					{"data": 1, "title": "Unique Usernames", "className": "small text-right"}
				]
			}));

			// Top IPs Table
			failedLoginsTables.topIPs = $('##tblTopIPs').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'topIPs';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "IP Address", 'className': 'small'},
					{"data": 1, "title": "Failed Attempts", "className": "small text-right"}
				]
			}));

			// IPs Multiple Accounts Table
			failedLoginsTables.ipsMultipleAccounts = $('##tblIpsMultipleAccounts').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'ipsMultipleAccounts';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "IP Address", 'className': 'small'},
					{"data": 1, "title": "Unique Usernames Attempted", "className": "small text-right"}
				]
			}));

			// Users Multiple IPs Table
			failedLoginsTables.usersMultipleIPs = $('##tblUsersMultipleIPs').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'usersMultipleIPs';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "Username", 'className': 'small'},
					{"data": 1, "title": "Unique IPs", "className": "small text-right"}
				]
			}));

			// Top User Agents Table
			failedLoginsTables.topUserAgents = $('##tblTopUserAgents').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'topUserAgents';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "User Agent", 'className': 'small'},
					{"data": 1, "title": "Failed Attempts", "className": "small text-right"}
				]
			}));

			// User Agents Multiple Accounts Table
			failedLoginsTables.userAgentsMultipleAccounts = $('##tblUserAgentsMultipleAccounts').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'userAgentsMultipleAccounts';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "User Agent", 'className': 'small'},
					{"data": 1, "title": "Unique Usernames Attempted", "className": "small text-right"}
				]
			}));

			// Anomalous User Agents Table
			failedLoginsTables.anomalousUserAgents = $('##tblAnomalousUserAgents').DataTable($.extend({}, commonConfig, {
				"ajax": {
					"url": baseURL,
					"type": "post",
					"data": function(d) {
						d.reportType = 'anomalousUserAgents';
						d.dateFrom = $('##dateFrom').val();
						d.dateTo = $('##dateTo').val();
					}
				},
				"columns": [
					{"data": 0, "title": "User Agent", 'className': 'small'},
					{"data": 1, "title": "Total Failed Attempts", "className": "small text-right"},
					{"data": 2, "title": "Unique IP Count", "className": "small text-right"}
				]
			}));
		}

		function refreshAllTables() {
			// Validate date range
			if (!$('##dateFrom').val() || !$('##dateTo').val()) {
				alert('Please select both From and To dates.');
				return;
			}

			// Refresh all tables
			$.each(failedLoginsTables, function(key, table) {
				table.ajax.reload();
			});
		}
	</script>
</cfsavecontent>
</cfoutput>
<cfhtmlhead text="#local.js#">
<cfoutput>
<div class="container-fluid">
	<div class="row">
		<div class="col-12">
			<div class="card card-box mb-3">
				<div class="card-header py-2 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">
						<i class="fa-light fa-shield-exclamation mr-1"></i> Failed Logins Security Analysis
					</div>
				</div>
				<div class="card-body">
					<div class="alert alert-info mb-3">
						<i class="fa-light fa-info-circle mr-1"></i> 
						<strong>Security Monitoring:</strong> This tool analyzes failed login attempts to identify potential security threats including brute-force attacks, credential stuffing, and password spraying attempts.
					</div>

					<!--- Filter Form --->
					<div class="card card-box mb-3">
						<div class="card-header bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								<i class="fa-light fa-magnifying-glass mr-1"></i> Filter Options
							</div>
						</div>
						<div class="card-body pb-3">
							<form id="frmFailedLoginsFilter" name="frmFailedLoginsFilter">
								<div class="form-group row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="dateFrom" id="dateFrom" value="#local.dateFrom#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="dateFrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="dateFrom">Date From</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="dateTo" id="dateTo" value="#local.dateTo#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="dateTo"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="dateTo">Date To</label>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<div class="col-md-12 text-right">
										<button type="button" name="btnRefreshData" onclick="refreshAllTables();" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Filter Data</button>
									</div>
								</div>
							</form>
						</div>
					</div>

					<!--- Charts Grid Layout --->
					<div class="row" id="chartsContainer">
						<!--- Row 1: Summary Stats --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-users mr-1"></i> Top Usernames with Failed Logins </h6>
								</div>
								<div class="card-body p-2">
									<table id="tblTopUsernames" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Username</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-key mr-1"></i> Top Passwords Attempted</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblTopPasswords" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Password</th>
												<th class="text-right">Unique Usernames</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 2: Time and Site Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-clock mr-1"></i> Failed Attempts Over Time</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblAttemptsOverTime" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Date</th>
												<th>Hour</th>
												<th class="text-right">Total Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-globe mr-1"></i> Failed Logins by Site</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblFailedBySite" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Site Code</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 3: Credential Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-fingerprint mr-1"></i> Unique User-Password Combinations</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblUniqueCombinations" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Total Failed Attempts</th>
												<th class="text-right">Unique Usernames</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-network-wired mr-1"></i> Top Attacking IP Addresses</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblTopIPs" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>IP Address</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 4: Advanced IP Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-users-slash mr-1"></i> IPs Attacking Multiple Accounts</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblIpsMultipleAccounts" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>IP Address</th>
												<th class="text-right">Unique Usernames Attempted</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-user-shield mr-1"></i> Users Attacked from Multiple IPs</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblUsersMultipleIPs" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Username</th>
												<th class="text-right">Unique IPs</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 5: User Agent Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-browser mr-1"></i> Top User-Agents for Failed Logins</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblTopUserAgents" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-robot mr-1"></i> User-Agents Attacking Multiple Accounts</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblUserAgentsMultipleAccounts" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Unique Usernames Attempted</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 6: Anomaly Detection --->
						<div class="col-lg-12 mb-3">
							<div class="card">
								<div class="card-header bg-light py-2">
									<h6 class="m-1"><i class="fa-light fa-exclamation-triangle mr-1"></i> Anomalous User-Agent to IP Address Ratio</h6>
								</div>
								<div class="card-body p-2">
									<table id="tblAnomalousUserAgents" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Total Failed Attempts</th>
												<th class="text-right">Unique IP Count</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>
