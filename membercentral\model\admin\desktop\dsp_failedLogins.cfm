<cfoutput>
<div class="container-fluid">
	<div class="row">
		<div class="col-12">
			<div class="card card-box mb-3">
				<div class="card-header py-2 bg-primary text-white">
					<div class="card-header--title font-weight-bold font-size-lg">
						<i class="fa-light fa-shield-exclamation"></i> Failed Logins Security Analysis
					</div>
				</div>
				<div class="card-body">
					<div class="alert alert-info mb-3">
						<i class="fa-light fa-info-circle"></i> 
						<strong>Security Monitoring:</strong> This tool analyzes failed login attempts to identify potential security threats including brute-force attacks, credential stuffing, and password spraying attempts.
					</div>

					<!--- Filter Form --->
					<div class="card card-box mb-3">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								<i class="fa-light fa-magnifying-glass"></i> Filter Options
							</div>
						</div>
						<div class="card-body pb-3">
							<form id="frmFailedLoginsFilter" name="frmFailedLoginsFilter">
								<div class="form-row">
									<div class="col-sm-3">
										<div class="form-group">
											<label for="dateFrom" class="font-weight-bold">Date From <span class="text-danger">*</span></label>
											<input type="text" name="dateFrom" id="dateFrom" class="form-control dateControl" value="#local.dateFrom#" required>
										</div>
									</div>
									<div class="col-sm-3">
										<div class="form-group">
											<label for="dateTo" class="font-weight-bold">Date To <span class="text-danger">*</span></label>
											<input type="text" name="dateTo" id="dateTo" class="form-control dateControl" value="#local.dateTo#" required>
										</div>
									</div>
									<div class="col-sm-6">
										<div class="form-group">
											<label class="font-weight-bold">&nbsp;</label>
											<div>
												<button type="button" id="btnRefreshData" class="btn btn-primary">
													<i class="fa-light fa-refresh"></i> Refresh All Data
												</button>
												<button type="button" id="btnExportData" class="btn btn-secondary ml-2">
													<i class="fa-light fa-download"></i> Export Summary
												</button>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>

					<!--- Charts Grid Layout --->
					<div class="row" id="chartsContainer">
						<!--- Row 1: Summary Stats --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-danger text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-users"></i> Top Usernames with Failed Logins</h6>
									<small>Brute-force attack indicators</small>
								</div>
								<div class="card-body p-2">
									<table id="tblTopUsernames" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Username</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-warning text-dark py-2">
									<h6 class="mb-0"><i class="fa-light fa-key"></i> Top Passwords Attempted</h6>
									<small>Password spraying attack indicators</small>
								</div>
								<div class="card-body p-2">
									<table id="tblTopPasswords" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Password</th>
												<th class="text-right">Unique Usernames</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 2: Time and Site Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-info text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-clock"></i> Failed Attempts Over Time</h6>
									<small>Attack activity spikes</small>
								</div>
								<div class="card-body p-2">
									<table id="tblAttemptsOverTime" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Date</th>
												<th>Hour</th>
												<th class="text-right">Total Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-success text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-globe"></i> Failed Logins by Site</h6>
									<small>Targeted site analysis</small>
								</div>
								<div class="card-body p-2">
									<table id="tblFailedBySite" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Site Code</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 3: Credential Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-secondary text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-fingerprint"></i> Unique User-Password Combinations</h6>
									<small>Credential stuffing indicators</small>
								</div>
								<div class="card-body p-2">
									<table id="tblUniqueCombinations" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Total Failed Attempts</th>
												<th class="text-right">Unique Usernames</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-dark text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-network-wired"></i> Top Attacking IP Addresses</h6>
									<small>Source IP analysis</small>
								</div>
								<div class="card-body p-2">
									<table id="tblTopIPs" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>IP Address</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 4: Advanced IP Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-danger text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-users-slash"></i> IPs Attacking Multiple Accounts</h6>
									<small>Credential stuffing/brute-force detection</small>
								</div>
								<div class="card-body p-2">
									<table id="tblIpsMultipleAccounts" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>IP Address</th>
												<th class="text-right">Unique Usernames Attempted</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-warning text-dark py-2">
									<h6 class="mb-0"><i class="fa-light fa-user-shield"></i> Users Attacked from Multiple IPs</h6>
									<small>Distributed brute-force detection</small>
								</div>
								<div class="card-body p-2">
									<table id="tblUsersMultipleIPs" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>Username</th>
												<th class="text-right">Unique IPs</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 5: User Agent Analysis --->
						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-info text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-browser"></i> Top User-Agents for Failed Logins</h6>
									<small>Browser/bot analysis</small>
								</div>
								<div class="card-body p-2">
									<table id="tblTopUserAgents" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Failed Attempts</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="col-lg-6 mb-3">
							<div class="card">
								<div class="card-header bg-primary text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-robot"></i> User-Agents Attacking Multiple Accounts</h6>
									<small>Automated attack detection</small>
								</div>
								<div class="card-body p-2">
									<table id="tblUserAgentsMultipleAccounts" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Unique Usernames Attempted</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>

						<!--- Row 6: Anomaly Detection --->
						<div class="col-lg-12 mb-3">
							<div class="card">
								<div class="card-header bg-secondary text-white py-2">
									<h6 class="mb-0"><i class="fa-light fa-exclamation-triangle"></i> Anomalous User-Agent to IP Address Ratio</h6>
									<small>Suspicious user-agent patterns</small>
								</div>
								<div class="card-body p-2">
									<table id="tblAnomalousUserAgents" class="table table-sm table-striped mb-0">
										<thead class="thead-light">
											<tr>
												<th>User Agent</th>
												<th class="text-right">Total Failed Attempts</th>
												<th class="text-right">Unique IP Count</th>
											</tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
$(document).ready(function() {
	// Initialize date pickers
	mca_setupDatePickerRangeFields('dateFrom','dateTo');
	mca_setupCalendarIcons('frmFailedLoginsFilter');
	
	// Initialize all data tables
	initializeFailedLoginsTables();
	
	// Refresh button handler
	$('##btnRefreshData').click(function() {
		refreshAllTables();
	});
	
	// Export button handler
	$('##btnExportData').click(function() {
		exportFailedLoginsData();
	});
});

var failedLoginsTables = {};

function initializeFailedLoginsTables() {
	var baseURL = '#local.filterFailedLoginsLink#';
	var commonConfig = {
		"processing": true,
		"serverSide": false,
		"pageLength": 10,
		"lengthMenu": [5, 10, 25, 50],
		"searching": false,
		"ordering": false,
		"info": false,
		"autoWidth": false,
		"responsive": true,
		"dom": "<'row'<'col-sm-6'l><'col-sm-6'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12'p>>"
	};
	
	// Top Usernames Table
	failedLoginsTables.topUsernames = $('##tblTopUsernames').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'topUsernames';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Username"},
			{"data": 1, "title": "Failed Attempts", "className": "text-right"}
		]
	}));
	
	// Top Passwords Table  
	failedLoginsTables.topPasswords = $('##tblTopPasswords').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post", 
			"data": function(d) {
				d.reportType = 'topPasswords';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Password"},
			{"data": 1, "title": "Unique Usernames", "className": "text-right"}
		]
	}));
	
	// Attempts Over Time Table
	failedLoginsTables.attemptsOverTime = $('##tblAttemptsOverTime').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'attemptsOverTime';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Date"},
			{"data": 1, "title": "Hour"},
			{"data": 2, "title": "Total Attempts", "className": "text-right"}
		]
	}));
	
	// Failed By Site Table
	failedLoginsTables.failedBySite = $('##tblFailedBySite').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'failedBySite';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Site Code"},
			{"data": 1, "title": "Failed Attempts", "className": "text-right"}
		]
	}));

	// Unique Combinations Table
	failedLoginsTables.uniqueCombinations = $('##tblUniqueCombinations').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'uniqueCombinations';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Total Failed Attempts", "className": "text-right"},
			{"data": 1, "title": "Unique Usernames", "className": "text-right"}
		]
	}));

	// Top IPs Table
	failedLoginsTables.topIPs = $('##tblTopIPs').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'topIPs';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "IP Address"},
			{"data": 1, "title": "Failed Attempts", "className": "text-right"}
		]
	}));

	// IPs Multiple Accounts Table
	failedLoginsTables.ipsMultipleAccounts = $('##tblIpsMultipleAccounts').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'ipsMultipleAccounts';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "IP Address"},
			{"data": 1, "title": "Unique Usernames Attempted", "className": "text-right"}
		]
	}));

	// Users Multiple IPs Table
	failedLoginsTables.usersMultipleIPs = $('##tblUsersMultipleIPs').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'usersMultipleIPs';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "Username"},
			{"data": 1, "title": "Unique IPs", "className": "text-right"}
		]
	}));

	// Top User Agents Table
	failedLoginsTables.topUserAgents = $('##tblTopUserAgents').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'topUserAgents';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "User Agent"},
			{"data": 1, "title": "Failed Attempts", "className": "text-right"}
		]
	}));

	// User Agents Multiple Accounts Table
	failedLoginsTables.userAgentsMultipleAccounts = $('##tblUserAgentsMultipleAccounts').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'userAgentsMultipleAccounts';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "User Agent"},
			{"data": 1, "title": "Unique Usernames Attempted", "className": "text-right"}
		]
	}));

	// Anomalous User Agents Table
	failedLoginsTables.anomalousUserAgents = $('##tblAnomalousUserAgents').DataTable($.extend({}, commonConfig, {
		"ajax": {
			"url": baseURL,
			"type": "post",
			"data": function(d) {
				d.reportType = 'anomalousUserAgents';
				d.dateFrom = $('##dateFrom').val();
				d.dateTo = $('##dateTo').val();
			}
		},
		"columns": [
			{"data": 0, "title": "User Agent"},
			{"data": 1, "title": "Total Failed Attempts", "className": "text-right"},
			{"data": 2, "title": "Unique IP Count", "className": "text-right"}
		]
	}));
}

function refreshAllTables() {
	// Validate date range
	if (!$('##dateFrom').val() || !$('##dateTo').val()) {
		alert('Please select both From and To dates.');
		return;
	}
	
	// Refresh all tables
	$.each(failedLoginsTables, function(key, table) {
		table.ajax.reload();
	});
}

function exportFailedLoginsData() {
	// Implementation for export functionality
	alert('Export functionality will be implemented in next phase.');
}
</script>
</cfoutput>
