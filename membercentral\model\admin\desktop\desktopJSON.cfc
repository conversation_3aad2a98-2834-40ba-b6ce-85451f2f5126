<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getScheduledTasks" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 2)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"name #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"taskInterval #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"case when nextRunDate is null then 1 else 0 end #arguments.event.getValue('orderDir')#, nextRunDate #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"dateStarted #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryScheduledTasks" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmptasks') IS NOT NULL
				DROP TABLE ##tmptasks;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskEnv') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskEnv;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskHistory') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskHistory;
			IF OBJECT_ID('tempdb..##tmpScheduledTasks') IS NOT NULL
				DROP TABLE ##tmpScheduledTasks;
			CREATE TABLE ##tmptasks (taskID int PRIMARY KEY);
			CREATE TABLE ##tmpScheduledTaskEnv (taskID int PRIMARY KEY, environments varchar(200));
			CREATE TABLE ##tmpScheduledTaskHistory (taskID int PRIMARY KEY, historyID int);
			CREATE TABLE ##tmpScheduledTasks (taskID int PRIMARY KEY, name varchar(100), taskCFC varchar(100), nextRunDate datetime, 
				taskInterval varchar(20), dateStarted datetime, statusName varchar(10), engine varchar(12), environments varchar(200), 
				isRunning bit, isPaused bit, row int);

			DECLARE @totalCount int;

			INSERT INTO ##tmptasks (taskID)
			select distinct t.taskID
			from dbo.scheduledTasks as t
			<cfif arguments.event.getValue('fEnv','') NEQ ''>
				inner join dbo.scheduledTaskEnvironments as te on te.taskID = t.taskID
					and te.environmentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fEnv')#">
			</cfif>
			where 1 = 1
			<cfif arguments.event.getValue('fTaskName','') neq ''>
				and t.[name] like <cfqueryparam value="%#replace(arguments.event.getValue('fTaskName'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
			</cfif>
			<cfif arguments.event.getValue('fCFCName','') neq ''>
				and t.taskCFC like <cfqueryparam value="%#replace(arguments.event.getValue('fCFCName'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
			</cfif>
			<cfif arguments.event.getValue('fNextRunDateFrom','') NEQ ''>
				and t.nextRunDate >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fNextRunDateFrom')#">
			</cfif>
			<cfif arguments.event.getValue('fNextRunDateTo','') NEQ ''>
				and t.nextRunDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fNextRunDateTo')# 23:59:59.997">
			</cfif>
			<cfif arguments.event.getValue('fEngine','') NEQ ''>
				and t.engineID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fEngine')#">
			</cfif>
			<cfif arguments.event.getValue('fPause','') NEQ ''>
				and t.isPaused = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('fPause')#">
			</cfif>;

			INSERT INTO ##tmpScheduledTaskEnv (taskID, environments)
			select tmp.taskID, STRING_AGG(pe.environmentName,'|')
			from ##tmptasks as tmp
			left outer join dbo.scheduledTaskEnvironments as ste 
				inner join dbo.platform_environments as pe on pe.environmentID = ste.environmentID
				on ste.taskID = tmp.taskid
			group by tmp.taskid;

			INSERT INTO ##tmpScheduledTaskHistory (taskID, historyID)
			select tmp.taskID, max(sth.historyID)
			from ##tmptasks as tmp
			inner join platformStatsMC.dbo.scheduledTaskHistory as sth on sth.taskID = tmp.taskid
			group by tmp.taskid;

			INSERT INTO ##tmpScheduledTasks (taskID, name, taskCFC, nextRunDate, taskInterval, dateStarted, statusName, engine, environments, isRunning, isPaused, row)
			SELECT taskid, name, taskCFC, nextRunDate, taskInterval, dateStarted, statusName, engine, environments, isRunning, isPaused,
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			FROM (
				select st.taskid, st.name, st.taskCFC, ste.engine, st.nextRunDate, 
					cast(st.interval as varchar(5)) + ' ' + case when st.interval = 1 then stt.singular else stt.name end as taskInterval, 
					sth.dateStarted, stst.statusName, se.environments, st.isRunning, st.isPaused
				from ##tmptasks as tmpT
				inner join dbo.scheduledTasks as st on st.taskID = tmpT.taskID
				inner join dbo.scheduledTaskIntervalTypes as stt on stt.intervalTypeID = st.intervalTypeID
				inner join dbo.scheduledTaskEngines as ste on ste.engineID = st.engineID
				inner join ##tmpScheduledTaskEnv as se on se.taskID = tmpT.taskID
				left outer join ##tmpScheduledTaskHistory as tmp 
					inner join platformStatsMC.dbo.scheduledTaskHistory as sth on sth.historyID = tmp.historyID
					inner join platformStatsMC.dbo.scheduledTaskStatusTypes stst on stst.statusTypeID = sth.statusTypeID
				on tmp.taskID = st.taskid
				where 1 = 1
				<cfif arguments.event.getValue('fLastRunDateFrom','') NEQ ''>
					and sth.dateStarted >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fLastRunDateFrom')#">
				</cfif>
				<cfif arguments.event.getValue('fLastRunDateTo','') NEQ ''>
					and sth.dateStarted <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fLastRunDateTo')# 23:59:59.997">
				</cfif>
			) as tmp2;

			SET @totalCount = @@ROWCOUNT;

			SELECT taskid, name, taskCFC, nextRunDate, taskInterval, dateStarted, statusName, engine, environments, isRunning, isPaused, @totalCount as totalCount
			FROM ##tmpScheduledTasks
			WHERE row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
			AND row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpScheduledTaskHistory') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskHistory;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskEnv') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskEnv;
			IF OBJECT_ID('tempdb..##tmpScheduledTasks') IS NOT NULL
				DROP TABLE ##tmpScheduledTasks;
			IF OBJECT_ID('tempdb..##tmptasks') IS NOT NULL
				DROP TABLE ##tmptasks;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		
		<cfloop query="local.qryScheduledTasks">
			<cfset local.envts = ''>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"production",'|')>
				<cfset local.envts = ListAppend(local.envts, 'P')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"newbeta",'|')>
				<cfset local.envts = ListAppend(local.envts, 'NB')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"beta",'|')>
				<cfset local.envts = ListAppend(local.envts, 'B')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"newdevelopment",'|')>
				<cfset local.envts = ListAppend(local.envts, 'ND')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"development",'|')>
				<cfset local.envts = ListAppend(local.envts, 'D')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"newtest",'|')>
				<cfset local.envts = ListAppend(local.envts, 'NT')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"test",'|')>
				<cfset local.envts = ListAppend(local.envts, 'T')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTasks.environments,"localDevelopment",'|')>
				<cfset local.envts = ListAppend(local.envts, 'L')>
			</cfif>
			
			<cfset local.arrData.append({
				"taskid": local.qryScheduledTasks.taskid,
				"name": local.qryScheduledTasks.name,
				"engine": local.qryScheduledTasks.engine,
				"taskCFC": local.qryScheduledTasks.taskCFC,
				"taskInterval": local.qryScheduledTasks.taskInterval,
				"nextRunDate": len(local.qryScheduledTasks.nextRunDate) GT 0 ? DateTimeFormat(local.qryScheduledTasks.nextRunDate,"m/d/yyyy h:nn tt") : "",
				"dateStarted": DateTimeFormat(local.qryScheduledTasks.dateStarted,"m/d/yyyy h:nn tt"),
				"statusName": local.qryScheduledTasks.statusName,
				"environments": local.envts ,
				"isRunning": local.qryScheduledTasks.isRunning,
				"isPaused": local.qryScheduledTasks.isPaused,
				"totalcount": local.qryScheduledTasks.totalcount,
				"DT_RowId": "row_#local.qryScheduledTasks.taskid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryScheduledTasks.totalcount),
			"recordsFiltered": val(local.qryScheduledTasks.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getScheduledTaskLogs" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sth.dateStarted")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryScheduledTaskLogs" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpScheduledTaskLogs') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskLogs;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskRunnerHeartbeats') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskRunnerHeartbeats;

			CREATE TABLE ##tmpScheduledTaskLogs (historyID int PRIMARY KEY, dateStarted datetime, execTimeMS int, dockerContainerID varchar(50), servername varchar(50), batchIdentifier varchar(50), itemCount int, statusName varchar(10), row int, totalCount int);
			CREATE TABLE ##tmpScheduledTaskRunnerHeartbeats (dockerContainerID varchar(50) PRIMARY KEY, dateCreated datetime, dateLastCheckin datetime, servername varchar(50));

			DECLARE @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300), @oneMonthAgo datetime = dateadd(month,-1,getdate());
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			
			INSERT INTO ##tmpScheduledTaskLogs (historyID, dateStarted, execTimeMS, dockerContainerID, servername, batchIdentifier, itemCount, statusName, row)
			SELECT sth.historyID, sth.dateStarted, DATEDIFF(MS,sth.dateStarted,sth.dateEnded) as execTimeMS, sth.dockerContainerID, sth.servername, sth.batchIdentifier, sth.itemCount, stt.statusName, 
				ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
			FROM dbo.scheduledTasks st
			INNER JOIN platformStatsMC.dbo.scheduledTaskHistory as sth on sth.taskID = st.taskid AND sth.dateStarted > @oneMonthAgo
			INNER JOIN platformStatsMC.dbo.scheduledTaskStatusTypes as stt on sth.statusTypeID = stt.statusTypeID
				WHERE st.taskid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskID',0)#">
			<cfif len(local.searchValue)>
				AND (sth.servername like @searchValue)
			</cfif>;

			SET @totalCount = @@rowcount;

			INSERT INTO ##tmpScheduledTaskRunnerHeartbeats (dockerContainerID, dateCreated, dateLastCheckin, servername)
			SELECT hb.dockerContainerID, hb.dateCreated, hb.dateLastCheckin, hb.servername
			FROM ##tmpScheduledTaskLogs sth
			INNER JOIN platformStatsMC.dbo.scheduledTaskRunnerHeartbeats hb on hb.dockerContainerID = sth.dockerContainerID
			GROUP BY hb.taskRunnerID, hb.dockerContainerID, hb.dateCreated, hb.dateLastCheckin, hb.servername;

			SELECT sth.historyID, sth.dateStarted, sth.execTimeMS, sth.dockerContainerID, sth.servername, sth.batchIdentifier, 
                sth.itemCount, sth.statusName, hb.dateLastCheckin, @totalCount AS totalCount
			FROM ##tmpScheduledTaskLogs sth
			LEFT OUTER JOIN ##tmpScheduledTaskRunnerHeartbeats hb on hb.dockerContainerID = sth.dockerContainerID
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpScheduledTaskRunnerHeartbeats') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskRunnerHeartbeats;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskLogs') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryScheduledTaskLogs">
			<cfset local.arrData.append({
				"historyID": local.qryScheduledTaskLogs.historyID,
				"dateStarted": DateTimeFormat(local.qryScheduledTaskLogs.dateStarted,"m/d/yyyy h:nn tt"),
				"execTimeMS": Numberformat(local.qryScheduledTaskLogs.execTimeMS,','),
				"dockerContainerID": local.qryScheduledTaskLogs.dockerContainerID,
				"servername": local.qryScheduledTaskLogs.servername,
				"batchIdentifier": local.qryScheduledTaskLogs.batchIdentifier,
				"itemcount": Numberformat(local.qryScheduledTaskLogs.itemCount,','),
				"statusName": local.qryScheduledTaskLogs.statusName,
				"dateLastCheckin": DateTimeFormat(local.qryScheduledTaskLogs.dateLastCheckin,'m/d/yy h:nn TT'),
				"totalcount": local.qryScheduledTaskLogs.totalcount,
				"DT_RowId": "scheduleTaskLogRow_#local.qryScheduledTaskLogs.historyID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryScheduledTaskLogs.totalcount),
			"recordsFiltered": val(local.qryScheduledTaskLogs.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getPermissionRoles" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qryPermissionRoleList = CreateObject("component","model.admin.desktop.desktop").getPermissionsFromFilters(event=arguments.event, operationMode="grid");
		</cfscript>

		<cfset local.arrData = []>
		<cfloop query="local.qryPermissionRoleList">
			<cfset local.arrData.append({
				"resourceType": local.qryPermissionRoleList.resourceType,
				"resourceTypeClassName": local.qryPermissionRoleList.resourceTypeClassName,
				"applicationType": local.qryPermissionRoleList.applicationType,
				"toolCFC": local.qryPermissionRoleList.toolCFC,
				"functionName": local.qryPermissionRoleList.functionName,
				"displayName": local.qryPermissionRoleList.displayName,
				"rolesList": local.qryPermissionRoleList.rolesList.replaceAll(',',', '),
				"DT_RowId": "permRolesRow_#local.qryPermissionRoleList.row#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryPermissionRoleList.totalCount),
			"recordsFiltered":  val(local.qryPermissionRoleList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getExecutionList" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>
		
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"st.name + ste.engine + st.taskCFC")>
		<cfset arrayAppend(local.arrCols,"st.interval")>
		<cfset arrayAppend(local.arrCols,"sth.dateStarted")>
		<cfset arrayAppend(local.arrCols,"DATEDIFF(MS,sth.dateStarted,sth.dateEnded)")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryScheduledTaskLogs" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpScheduledTaskLogs') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskLogs;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskRunnerHeartbeats') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskRunnerHeartbeats;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskEnv') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskEnv;

			CREATE TABLE ##tmpScheduledTaskLogs (taskid int, name varchar(100), taskCFC varchar(100), isRunning bit, engine varchar(12), 
				taskInterval varchar(20), historyID int PRIMARY KEY, dateStarted datetime, execTimeMS int, dockerContainerID varchar(50), 
				servername varchar(50), batchIdentifier varchar(50), itemCount int, statusName varchar(10), row int, totalCount int);
			CREATE TABLE ##tmpScheduledTaskRunnerHeartbeats (dockerContainerID varchar(50) PRIMARY KEY, dateCreated datetime, 
				dateLastCheckin datetime, servername varchar(50));
			
			DECLARE @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300), @oneMonthAgo datetime = dateadd(month,-1,getdate());
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			
			INSERT INTO ##tmpScheduledTaskLogs (taskid, name, taskCFC, isRunning, engine,taskInterval,  historyID, dateStarted, execTimeMS, dockerContainerID, servername, batchIdentifier, itemCount, statusName, row)
			SELECT st.taskid, st.name, st.taskCFC, st.isRunning, ste.engine, 
				cast(st.interval as varchar(5)) + ' ' + case when st.interval = 1 then stit.singular else stit.name end as taskInterval, 
				sth.historyID, sth.dateStarted, DATEDIFF(MS,sth.dateStarted,sth.dateEnded) as execTimeMS, sth.dockerContainerID, sth.servername, sth.batchIdentifier, sth.itemCount, stt.statusName, 
				ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
			FROM dbo.scheduledTasks st
			INNER JOIN dbo.scheduledTaskEngines as ste on ste.engineID = st.engineID
			inner join dbo.scheduledTaskIntervalTypes as stit on stit.intervalTypeID = st.intervalTypeID
			INNER JOIN platformStatsMC.dbo.scheduledTaskHistory as sth on sth.taskID = st.taskid AND sth.dateStarted > @oneMonthAgo
			INNER JOIN platformStatsMC.dbo.scheduledTaskStatusTypes as stt on sth.statusTypeID = stt.statusTypeID
			WHERE 1 = 1
			<cfif arguments.event.getValue('fTaskName','') neq ''>
				and st.[name] like <cfqueryparam value="%#replace(arguments.event.getValue('fTaskName'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
			</cfif>
			<cfif arguments.event.getValue('fCFCName','') neq ''>
				and st.taskCFC like <cfqueryparam value="%#replace(arguments.event.getValue('fCFCName'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
			</cfif>
			<cfif arguments.event.getValue('fDateStartedFrom','') NEQ ''>
				and sth.dateStarted >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fDateStartedFrom')#">
			</cfif>
			<cfif arguments.event.getValue('fDateStartedTo','') NEQ ''>
				and sth.dateStarted <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fDateStartedTo')# 23:59:59.997">
			</cfif>
			<cfif arguments.event.getValue('fEngine','') NEQ ''>
				and st.engineID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fEngine')#">
			</cfif>
			<cfif arguments.event.getValue('intervalFrom','') NEQ ''>
				and DATEDIFF(MS,sth.dateStarted,sth.dateEnded) >= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('intervalFrom')#">
			</cfif>
			<cfif arguments.event.getValue('intervalTo','') NEQ ''>
				and DATEDIFF(MS,sth.dateStarted,sth.dateEnded) <= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('intervalTo')#">
			</cfif>;
			
			SET @totalCount = @@rowcount;

			CREATE NONCLUSTERED INDEX [IX_tmpScheduledTaskLogs__row__IncludesAll] ON ##tmpScheduledTaskLogs ([row])
				INCLUDE ([taskid],[name],[taskCFC],[isRunning],[engine],[taskInterval],[historyID],[dateStarted],[execTimeMS],[dockerContainerID],[servername],[batchIdentifier],[itemCount],[statusName]);

			INSERT INTO ##tmpScheduledTaskRunnerHeartbeats (dockerContainerID, dateCreated, dateLastCheckin, servername)
			SELECT hb.dockerContainerID, hb.dateCreated, hb.dateLastCheckin, hb.servername
			FROM ##tmpScheduledTaskLogs sth
			INNER JOIN platformStatsMC.dbo.scheduledTaskRunnerHeartbeats hb on hb.dockerContainerID = sth.dockerContainerID
			GROUP BY hb.taskRunnerID, hb.dockerContainerID, hb.dateCreated, hb.dateLastCheckin, hb.servername;

			SELECT sth.taskid, sth.name, sth.isRunning, sth.taskCFC, sth.taskInterval, sth.engine, sth.historyID, sth.dateStarted, sth.execTimeMS, sth.dockerContainerID, sth.servername, sth.batchIdentifier, 
                sth.itemCount, sth.statusName, hb.dateLastCheckin, @totalCount AS totalCount, STRING_AGG(pe.environmentName,'|') as environments
			FROM ##tmpScheduledTaskLogs sth
			LEFT OUTER JOIN ##tmpScheduledTaskRunnerHeartbeats hb on hb.dockerContainerID = sth.dockerContainerID
			LEFT OUTER JOIN dbo.scheduledTaskEnvironments as ste 
				inner join dbo.platform_environments as pe on pe.environmentID = ste.environmentID
				on ste.taskID = sth.taskid			
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			group by sth.taskid,sth.name, sth.isRunning,sth.taskCFC, sth.taskInterval, sth.engine,sth.historyID, sth.dateStarted, sth.execTimeMS, sth.dockerContainerID, sth.servername, sth.batchIdentifier, 
                sth.itemCount, sth.statusName, hb.dateLastCheckin,row
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpScheduledTaskRunnerHeartbeats') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskRunnerHeartbeats;
			IF OBJECT_ID('tempdb..##tmpScheduledTaskLogs') IS NOT NULL
				DROP TABLE ##tmpScheduledTaskLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryScheduledTaskLogs">
			<cfset local.envts = ''>
			<cfif listFindNoCase(local.qryScheduledTaskLogs.environments,"production",'|')>
				<cfset local.envts = ListAppend(local.envts, 'P')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTaskLogs.environments,"beta",'|') OR listFindNoCase(local.qryScheduledTaskLogs.environments,"newbeta",'|')>
				<cfset local.envts = ListAppend(local.envts, 'B')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTaskLogs.environments,"development",'|') OR listFindNoCase(local.qryScheduledTaskLogs.environments,"newdevelopment",'|')>
				<cfset local.envts = ListAppend(local.envts, 'D')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTaskLogs.environments,"test",'|') OR listFindNoCase(local.qryScheduledTaskLogs.environments,"newtest",'|')>>
				<cfset local.envts = ListAppend(local.envts, 'T')>
			</cfif>
			<cfif listFindNoCase(local.qryScheduledTaskLogs.environments,"localDevelopment",'|')>
				<cfset local.envts = ListAppend(local.envts, 'L')>
			</cfif>
			<cfset local.arrData.append({
				"taskid": local.qryScheduledTaskLogs.taskid,
				"historyID": local.qryScheduledTaskLogs.historyID,
				"name": local.qryScheduledTaskLogs.name,
				"engine": local.qryScheduledTaskLogs.engine,
				"taskCFC": local.qryScheduledTaskLogs.taskCFC,
				"taskInterval": local.qryScheduledTaskLogs.taskInterval,
				"environments": local.envts ,
				"dateStarted": DateTimeFormat(local.qryScheduledTaskLogs.dateStarted,"m/d/yyyy h:nn tt"),
				"execTimeMS": Numberformat(local.qryScheduledTaskLogs.execTimeMS,','),
				"dockerContainerID": local.qryScheduledTaskLogs.dockerContainerID,
				"servername": local.qryScheduledTaskLogs.servername,
				"batchIdentifier": local.qryScheduledTaskLogs.batchIdentifier,
				"itemcount": Numberformat(local.qryScheduledTaskLogs.itemCount,','),
				"statusName": local.qryScheduledTaskLogs.statusName,
				"dateLastCheckin": DateTimeFormat(local.qryScheduledTaskLogs.dateLastCheckin,'m/d/yy h:nn TT'),
				"totalcount": local.qryScheduledTaskLogs.totalcount,
				"isRunning": local.qryScheduledTaskLogs.isRunning,
				"DT_RowId": "scheduleTaskLogRow_#local.qryScheduledTaskLogs.historyID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryScheduledTaskLogs.totalcount),
			"recordsFiltered": val(local.qryScheduledTaskLogs.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getFilterCondCacheLogResult" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',100))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		<cftry>
			<cfset local.arrCols = arrayNew(1)>
			<cfif local.siteCode eq "MC">
				<cfset arrayAppend(local.arrCols,"o.orgcode")>
			</cfif>
			<cfset arrayAppend(local.arrCols,"cp.procname")>
			<cfset arrayAppend(local.arrCols,"avg(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"min(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"max(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"stdev(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"count(cp.logID)")>
			<cfset arrayAppend(local.arrCols,"sum(cp.timeMS)")>
			<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

			<cfset local.dateFrom = replace(arguments.event.getTrimValue('fCondStatsDateFrom',''),' - ',' ')>
			<cfset local.dateTo = replace(arguments.event.getTrimValue('fCondStatsDateTo',''),' - ',' ')>

			<cfset local.includeOrgCodeColumn = local.siteCode neq "MC" OR (local.siteCode eq "MC" and arguments.event.getTrimValue('combineCondOrgsToggle',0) EQ 0)>

			<cfquery name="local.qryCacheLogData" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT <cfif local.includeOrgCodeColumn>o.orgcode, </cfif>
					cp.procname, avg(cp.timeMS) as avgMS, min(cp.timeMS) as minMS, max(cp.timeMS) as maxMS, 
					stdev(cp.timeMS) as standardDeviation, count(cp.logID) as numRuns, sum(cp.timeMS) as sumMS
				FROM dbo.cache_conditionsLogRunProcs as cp 
				INNER JOIN dbo.cache_conditionsLogRun as cr ON cp.runID = cr.runID 
					<cfif local.siteCode eq "MC" and listLen(arguments.event.getTrimValue('fCondStatsOrgID',''))>
						and cr.orgID IN (<cfqueryparam value="#arguments.event.getTrimValue('fCondStatsOrgID')#" cfsqltype="CF_SQL_INTEGER" list="true">)
					<cfelseif local.siteCode neq "MC">
						and cr.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				INNER JOIN membercentral.dbo.organizations o on o.orgID = cr.orgID
				WHERE 1=1
				<cfif len(arguments.event.getTrimValue('fCondStatsProcName'))>
					and cp.procname LIKE <cfqueryparam value="%#arguments.event.getTrimValue('fCondStatsProcName')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(trim(local.dateFrom))>
					and cp.logDate >= <cfqueryparam value="#local.dateFrom#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				<cfif len(trim(local.dateTo))>
					and cp.logDate <= <cfqueryparam value="#local.dateTo#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY <cfif local.includeOrgCodeColumn>o.orgcode, </cfif>cp.procname
				ORDER BY #preserveSingleQuotes(local.orderby)#;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['recordsTotal'] = local.qryCacheLogData.recordcount>
			<cfset local.strReturn['recordsFiltered'] = local.qryCacheLogData.recordcount>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryCacheLogData">
				<cfset local.thisElement = { }>
				<cfset local.thisElement['orgcode'] = (local.includeOrgCodeColumn ? local.qryCacheLogData.orgcode : 'Combined')>
				<cfset local.thisElement['procname'] = local.qryCacheLogData.procname>
				<cfset local.thisElement['avgMS'] = local.qryCacheLogData.avgMS>
				<cfset local.thisElement['minMS'] = local.qryCacheLogData.minMS>
				<cfset local.thisElement['maxMS'] = local.qryCacheLogData.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryCacheLogData.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryCacheLogData.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryCacheLogData.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getFilterGroupCacheLogResult" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',100))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		<cftry>
			<cfset local.arrCols = arrayNew(1)>
			<cfif local.siteCode eq "MC">
				<cfset arrayAppend(local.arrCols,"o.orgcode")>
			</cfif>
			<cfset arrayAppend(local.arrCols,"cp.stepCode")>
			<cfset arrayAppend(local.arrCols,"avg(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"min(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"max(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"stdev(cp.timeMS)")>
			<cfset arrayAppend(local.arrCols,"count(cp.logID)")>
			<cfset arrayAppend(local.arrCols,"sum(cp.timeMS)")>
			<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

			<cfset local.dateFrom = replace(arguments.event.getTrimValue('fGroupStatsDateFrom',''),' - ',' ')>
			<cfset local.dateTo = replace(arguments.event.getTrimValue('fGroupStatsDateTo',''),' - ',' ')>

			<cfset local.includeOrgCodeColumn = local.siteCode neq "MC" OR (local.siteCode eq "MC" and arguments.event.getTrimValue('combineGroupOrgsToggle',0) EQ 0)>

			<cfquery name="local.qryCacheLogData" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT <cfif local.includeOrgCodeColumn>o.orgcode, </cfif>
					cp.stepCode, avg(cp.timeMS) as avgMS, min(cp.timeMS) as minMS, max(cp.timeMS) as maxMS, 
					stdev(cp.timeMS) as standardDeviation, count(cp.logID) as numRuns, sum(cp.timeMS) as sumMS
				FROM dbo.cache_groupsLogRunSteps as cp 
				INNER JOIN dbo.cache_groupsLogRun as cr ON cp.runID = cr.runID 
					<cfif local.siteCode eq "MC" and listLen(arguments.event.getTrimValue('fGroupStatsOrgID',''))>
						and cr.orgID IN (<cfqueryparam value="#arguments.event.getTrimValue('fGroupStatsOrgID')#" cfsqltype="CF_SQL_INTEGER" list="true">)
					<cfelseif local.siteCode neq "MC">
						and cr.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				INNER JOIN membercentral.dbo.organizations o on o.orgID = cr.orgID
				WHERE 1=1
				<cfif len(arguments.event.getTrimValue('fGroupStepCode'))>
					and cp.stepCode LIKE <cfqueryparam value="%#arguments.event.getTrimValue('fGroupStepCode')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(trim(local.dateFrom))>
					and cp.logDate >= <cfqueryparam value="#local.dateFrom#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				<cfif len(trim(local.dateTo))>
					and cp.logDate <= <cfqueryparam value="#local.dateTo#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY <cfif local.includeOrgCodeColumn>o.orgcode, </cfif>cp.stepCode
				ORDER BY #preserveSingleQuotes(local.orderby)#;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strGroupStatCodes = {
				dequeueSection1: "initial item group into temp table",
				dequeueSection2: "expand to other org members",
				dequeueSection3: "expand to other member entries",
				getOrgMemberCount: "get count of non-deleted org members",
				runLogging: "logging the run, members, and initial steps",
				loadConditionsCache: "load memberConditions into temp table",
				loadRuleGroups: "load rule to group mapping",
				loadRuleInfo: "load rule info",
				getVirtualAssign: "get virtual assignments for members",
				getRuleSQLSection4: "get ruleSQL for runSection=4 rules",
				expandRules: "expand rules and conditions",
				getConditionSetInfo: "get condition and set info",
				setRunSection5: "simple rules that use exclude: include AND; exclude AND",
				setRunSection6: "simple rules that use exclude: include AND; exclude OR (+ single condition ANDs)",
				setRunSection7: "simple rules that use exclude: include OR (+ single condition ANDs); exclude AND",
				setRunSection8: "simple rules that use exclude: include OR (+ single condition ANDs); exclude OR (+ single condition ANDs)",
				setRunSection9: "simple rules that use exclude: no include; exclude AND",
				setRunSection10: "simple rules that use exclude: no include; exclude OR (+ single condition ANDs)",
				setRunSection11: "non-simple rules",
				processSection1: "run all one-condition include rules",
				processSection2: "run all AND multi condition include rules",
				processSection3: "run all OR multi condition include rules",
				processSection5: "run simple rules that use include AND; exclude AND",
				processSection6: "run simple rules that use include AND; exclude OR",
				processSection7: "run simple rules that use include OR; exclude AND",
				processSection8: "run simple rules that use include OR; exclude OR",
				processSection9: "run simple rules with NO include; exclude AND",
				processSection10: "run simple rules with NO include; exclude OR",
				processSection11a: "complex rules: all AND multi condition include sets",
				processSection11b: "complex rules: run all OR multi condition include sets",
				processSection11c: "complex rules: run all AND multi condition exclude sets",
				processSection11d: "complex rules: run all OR multi condition exclude sets",
				processSection11e: "complex rules (sets of sets): get sets now ready to run",
				processSection11f: "complex rules (sets of sets): run all AND childset include rules",
				processSection11g: "complex rules (sets of sets): run all OR childset include rules",
				processSection11h: "complex rules (sets of sets): run all AND childset exclude rules",
				processSection11i: "complex rules (sets of sets): run all OR childset exclude rules",
				processSection11j: "complex rules: store members matching rules",
				processRuleByRule: "run remaining multi-condition rules",
				expandGroups: "expand groups",
				getManualAssign: "get manual assignments for members",
				getSystemGroupAssignments: "get public, guests, and users assignments",
				modifyMemberGroups: "modify member groups table",
				runWebHooks: "run the memberGroupChanged hook for each site in the org",
				modifyGroupPrints: "Modify group prints"
			}>

			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['recordsTotal'] = local.qryCacheLogData.recordcount>
			<cfset local.strReturn['recordsFiltered'] = local.qryCacheLogData.recordcount>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryCacheLogData">
				<cfset local.thisElement = { }>
				<cfset local.thisElement['orgcode'] = (local.includeOrgCodeColumn ? local.qryCacheLogData.orgcode : 'Combined')>
				<cfset local.thisElement['stepcode'] = local.qryCacheLogData.stepCode>
				<cfset local.thisElement['stepcodedesc'] =  (structKeyExists(local.strGroupStatCodes, local.qryCacheLogData.stepCode) ? local.strGroupStatCodes[local.qryCacheLogData.stepCode] : "")>
				<cfset local.thisElement['avgMS'] = local.qryCacheLogData.avgMS>
				<cfset local.thisElement['minMS'] = local.qryCacheLogData.minMS>
				<cfset local.thisElement['maxMS'] = local.qryCacheLogData.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryCacheLogData.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryCacheLogData.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryCacheLogData.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getSWCertLogs" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode');
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 2)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfif local.siteCode eq "MC">
			<cfset arrayAppend(local.arrCols,"siteCode #arguments.event.getValue('orderDir')#, certFileName")>
		<cfelse>
			<cfset arrayAppend(local.arrCols,"certFileName #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"avgMS #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"numRuns #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"sumMS #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qrySWCertLogs" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;
			CREATE TABLE ##tmpLogs (siteCode varchar(10), certFileName varchar(40), avgMS int, numRuns int, sumMS int);

			DECLARE @totalCount int;

			INSERT INTO ##tmpLogs (siteCode, certFileName, avgMS, numRuns, sumMS)
			SELECT p.orgCode, l.certFileName, avg(timeMS), count(logID), sum(timeMS)
			FROM dbo.sw_CertificateLog AS l
			INNER JOIN seminarWeb.dbo.tblEnrollments AS e ON e.enrollmentID = l.enrollmentID
			INNER JOIN seminarWeb.dbo.tblParticipants AS p ON p.participantID = e.participantID
			WHERE 1 = 1
			<cfif local.siteCode eq "MC" and listLen(arguments.event.getValue('siteCodes',''))>
				AND p.orgCode IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('siteCodes')#" list="true">)
			<cfelseif local.siteCode neq "MC">
				AND p.orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.sitecode')#">
			</cfif>
			<cfif arguments.event.getValue('fCertFiles','') neq ''>
				AND l.certFileName IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('fCertFiles')#" list="true">)
			</cfif>
			<cfif arguments.event.getValue('fDateFrom','') NEQ ''>
				AND l.dateStarted >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fDateFrom')#">
			</cfif>
			<cfif arguments.event.getValue('fDateTo','') NEQ ''>
				AND l.dateStarted <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fDateTo')# 23:59:59.997">
			</cfif>
			GROUP BY p.orgCode, l.certFileName;

			SET @totalCount = @@ROWCOUNT;

			SELECT siteCode, certFileName, avgMS, numRuns, sumMS, row, @totalCount AS totalCount
			FROM (
				SELECT siteCode, certFileName, avgMS, numRuns, sumMS, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
				FROM ##tmpLogs
			) tmp
			WHERE row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
			AND row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>

		<cfloop query="local.qrySWCertLogs">
			<cfif local.siteCode EQ 'MC'>
				<cfset local.certFileName = "#local.qrySWCertLogs.siteCode# - #local.qrySWCertLogs.certFileName#">
			<cfelse>
				<cfset local.certFileName = local.qrySWCertLogs.certFileName>
			</cfif>
			<cfset local.arrData.append({
				"certFileName": local.certFileName,
				"avgMS": local.qrySWCertLogs.avgMS,
				"numRuns": local.qrySWCertLogs.numRuns,
				"sumMS": local.qrySWCertLogs.sumMS
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySWCertLogs.totalcount),
			"recordsFiltered": val(local.qrySWCertLogs.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="get2FALogs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode');
			local.gridMode = arguments.event.getValue('gridMode','memberLogins');

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 2)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfif local.gridMode eq "mcPlatformWideGrid" and local.siteCode eq "MC">
			<cfset arrayAppend(local.arrCols,"siteCode #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"dateEntered #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"memberName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"type #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"methodName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"success #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfset local.fDateFrom = arguments.event.getValue('fDateFrom','')>
		<cfset local.fDateTo = arguments.event.getValue('fDateTo','')>
		<cfif local.siteCode eq "MC">
			<cfset local.fSiteID = arguments.event.getValue('fSiteID',0)>
		<cfelse>
			<cfset local.fSiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode=local.siteCode)>
		</cfif>
		<cfset local.fRequestType = arguments.event.getValue('fRequestType','')>

		<cfquery name="local.qry2FALogs" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;
			CREATE TABLE ##tmpLogs (siteCode varchar(10), dateEntered datetime, memberID int, memberOrgID int, memberName varchar(200), memberCompany varchar(200), type varchar(25),
				methodName varchar(50), twilioChannel varchar(20), success bit);

			DECLARE @totalCount int;

			INSERT INTO ##tmpLogs (siteCode, dateEntered, memberID, memberOrgID, memberName, memberCompany, type, methodName, twilioChannel, success)
			SELECT s.siteCode, r.dateEntered, mActive.memberID, mActive.orgID, mActive.firstName + ' ' + mActive.lastName AS memberName, mActive.company, r.[type],
				vm.methodName, CASE r.twilioChannel WHEN 'sms' THEN 'SMS' WHEN 'call' THEN 'Voice' WHEN 'whatsapp' THEN 'WhatsApp' ELSE '' END,
				r.success
			FROM (
				<cfif local.fRequestType eq "" OR local.fRequestType eq "Code Request">
					SELECT siteID, dateEntered, 'Code Request' AS [type], memberID,
						twilioChannel, NULL AS success, verificationMethodID
					FROM dbo.platform_verificationMethodCodeRequests
				</cfif>
				<cfif local.fRequestType eq "">
					UNION
				</cfif>
				<cfif local.fRequestType eq "" OR local.fRequestType eq "Validation Attempt">
					SELECT siteID, dateEntered, 'Validation Attempt' AS [type], memberID,
						NULL AS twilioChannel, success, verificationMethodID
					FROM dbo.platform_verificationMethodValidateRequests
				</cfif>
			) r
			INNER JOIN membercentral.dbo.siteLoginPolicyVerificationMethods AS lpvm ON lpvm.policyMethodID = r.verificationMethodID
			INNER JOIN membercentral.dbo.platform_verificationMethods AS vm ON vm.verificationMethodID = lpvm.verificationMethodID
			INNER JOIN membercentral.dbo.sites AS s ON s.siteID = r.siteID
			INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = r.memberID 
			INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
			WHERE 1 = 1
			<cfif local.fSiteID gt 0>
				AND r.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fSiteID#">
			</cfif>
			<cfif len(local.fDateFrom)>
				AND r.dateEntered >= <cfqueryparam cfsqltype="cf_sql_date" value="#local.fDateFrom#">
			</cfif>
			<cfif len(local.fDateTo)>
				AND r.dateEntered <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.fDateTo# 23:59:59.997">
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT siteCode, dateEntered, memberID, memberOrgID, memberName, memberCompany, [type], methodName, twilioChannel, success, row, @totalCount AS totalCount
			FROM (
				SELECT siteCode, dateEntered, memberID, memberOrgID, memberName, memberCompany, [type], methodName, twilioChannel, success,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
				FROM ##tmpLogs
			) tmp
			WHERE row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
			AND row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		
		<cfloop query="local.qry2FALogs">
			<cfset local.arrData.append({
				"sitecode": local.qry2FALogs.siteCode,
				"dateentered": DateTimeFormat(local.qry2FALogs.dateEntered,"m/d/yyyy h:nn tt"),
				"memberid": local.qry2FALogs.memberID,
				"memberorgid": local.qry2FALogs.memberOrgID,
				"membername": local.qry2FALogs.memberName,
				"membercompany": local.qry2FALogs.memberCompany,
				"type": local.qry2FALogs.type,
				"methodname": local.qry2FALogs.methodName,
				"twiliochannel": local.qry2FALogs.twilioChannel,
				"success": local.qry2FALogs.success
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qry2FALogs.totalcount),
			"recordsFiltered": val(local.qry2FALogs.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReportLogs" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 3)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.fSiteIDList = arguments.event.getValue('mc_siteInfo.sitecode') eq "MC" ? arguments.event.getValue('fSiteID','') : toString(arguments.event.getValue('mc_siteInfo.siteID'));
			local.fToolTypeIDList = arguments.event.getTrimValue('fRptType','');
			local.fRptName = arguments.event.getTrimValue('fRptName','');
			local.fdaterunfrom = arguments.event.getTrimValue('fdaterunfrom','');
			local.fdaterunto = arguments.event.getTrimValue('fdaterunto','');
			local.frptformat = arguments.event.getTrimValue('frptformat','');
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sr.reportName #arguments.event.getValue('orderDir')#, tt.toolDesc #arguments.event.getValue('orderDir')#")>
		<cfif arguments.event.getValue('mc_siteInfo.sitecode') eq "MC">
			<cfset arrayAppend(local.arrCols,"s.siteCode #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"rl.dateRun #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"rl.runTimeMS+isnull(rl.runTimeBCPtoCSV,0) #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryRptLogs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;
			CREATE TABLE ##tmpLogs (reportID int, siteCode varchar(10), reportName varchar(200), toolType varchar(100), 
				toolDesc varchar(200), dateRun datetime, runTimeMS int, runTimeBCPtoCSV int, fmt varchar(9), 
				memberName varchar(210), rowID int INDEX IX_tmpLogs_rowID);

			DECLARE @totalCount int;

			INSERT INTO ##tmpLogs (reportID, siteCode, reportName, toolType, toolDesc, dateRun, runTimeMS, runTimeBCPtoCSV, fmt, memberName, rowID)
			SELECT rl.reportID, s.siteCode, sr.reportName, tt.toolType, tt.toolDesc, rl.dateRun, rl.runTimeMS, rl.runTimeBCPtoCSV, rl.[format], 
				mActive.firstName + ' ' + mActive.lastName + ' (' + mActive.memberNumber + ')',
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
			FROM platformStatsMC.dbo.rpt_runLog AS rl
			INNER JOIN dbo.rpt_SavedReports AS sr ON sr.reportID = rl.reportID
			INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = sr.toolTypeID
			INNER JOIN dbo.ams_members AS m ON m.memberID = rl.memberID
			INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
			INNER JOIN dbo.sites AS s ON s.siteID = rl.siteID
			WHERE rl.runTimeMS IS NOT NULL
			<cfif local.fSiteIDList.len()>
				AND s.siteID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.fSiteIDList#">)
			</cfif>
			<cfif local.fToolTypeIDList.len()>
				AND tt.toolTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.fToolTypeIDList#">)
			</cfif>
			<cfif local.fRptName.len()>
				AND sr.reportName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.fRptName#%">
			</cfif>
			<cfif local.fdaterunfrom.len()>
				AND rl.dateRun >= <cfqueryparam cfsqltype="cf_sql_date" value="#local.fdaterunfrom#">
			</cfif>
			<cfif local.fdaterunto.len()>
				AND rl.dateRun <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fdaterunto# 23:59:59.997">
			</cfif>
			<cfif local.frptformat.len()>
				AND rl.[format] in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.frptformat#" list="true">)
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT reportID, siteCode, reportName, toolType, toolDesc, dateRun, runTimeMS, runTimeBCPtoCSV, runTimeMS+isnull(runTimeBCPtoCSV,0) as totalMS, 
				fmt, memberName, rowID, @totalCount AS totalCount
			FROM ##tmpLogs
			WHERE rowID > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
			AND rowID <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			ORDER by rowID;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryRptLogs">
			<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryRptLogs.siteCode)>
			<cfset local.reportLink = local.mc_siteinfo.scheme & "://" & local.mc_siteinfo.mainhostname & buildLinkToTool(toolType='#local.qryRptLogs.toolType#',mca_ta='showReport')>

			<cfset local.arrData.append({
				"sitecode": local.qryRptLogs.siteCode,
				"reportname": local.qryRptLogs.reportName,
				"tooltype": local.qryRptLogs.toolType,
				"tooldesc": local.qryRptLogs.toolDesc,
				"reporturl": "#local.reportLink#&rptID=#local.qryRptLogs.reportID#",
				"daterun": DateTimeFormat(local.qryRptLogs.dateRun,"m/d/yyyy h:nn tt"),
				"totalms": local.qryRptLogs.totalMS & " ms",
				"runtimems": local.qryRptLogs.runTimeMS & " ms",
				"runTimebcptocsv": local.qryRptLogs.runTimeBCPtoCSV & " ms",
				"fmt": local.qryRptLogs.fmt,
				"membername": local.qryRptLogs.memberName
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRptLogs.totalcount),
			"recordsFiltered": val(local.qryRptLogs.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRuleConditionCountResult" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.thisSiteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.orgIDList = local.thisSiteCode eq "MC" ? arguments.event.getTrimValue('fOrgID') : arguments.event.getValue('mc_siteInfo.orgID')>

		<cfstoredproc procedure="ams_tuneVirtualGroupAssignments" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.orgIDList#">
			<cfprocresult name="local.qryVirtualGrpRules" resultset="1">
		</cfstoredproc>

		<cfset local.data = []>
		<cfif local.qryVirtualGrpRules.recordCount>
			<cfloop query="local.qryVirtualGrpRules">
				<cfset local.tmpStr = {
					"rc": local.qryVirtualGrpRules.rc,
					"d": local.qryVirtualGrpRules.d,
					"DT_RowId": "ruleCount#local.qryVirtualGrpRules.id#"
				}>
				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": val(local.qryVirtualGrpRules.recordCount),
			"recordsFiltered": val(local.qryVirtualGrpRules.recordCount),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getAuthorizePaymentProfiles" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.arrMerchantProfilesList = CreateObject("component","model.admin.desktop.desktop").getMerchantProfilesListFromFilters(event=arguments.event, mode="grid")>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrMerchantProfilesList) GT 0 ? val(local.arrMerchantProfilesList[1].totalCount) : 0,
			"recordsFiltered": arrayLen(local.arrMerchantProfilesList) GT 0 ? val(local.arrMerchantProfilesList[1].totalCount) : 0,
			"data": local.arrMerchantProfilesList
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getFailedLoginsData" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			// Restrict to SuperUsers only
			if (not application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
				local.returnStruct = {
					"error": "Access denied: SuperUser privileges required"
				};
				return serializeJSON(local.returnStruct);
			}

			local.dateFrom = arguments.event.getValue('dateFrom', '');
			local.dateTo = arguments.event.getValue('dateTo', '');

			// Validate required parameters
			if (not len(local.dateFrom) or not len(local.dateTo)) {
				local.returnStruct = {
					"error": "Missing required parameters"
				};
				return serializeJSON(local.returnStruct);
			}
		</cfscript>

		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			EXEC dbo.admin_getFailedLoginsData
				@dateFrom = <cfqueryparam value="#local.dateFrom#" cfsqltype="cf_sql_timestamp">,
				@dateTo = <cfqueryparam value="#local.dateTo#" cfsqltype="cf_sql_timestamp">,
				@reportType = 'all'
		</cfquery>

		<cfscript>
			// Group data by reportType
			local.datasets = structNew();

			for (local.row in local.qryData) {
				local.reportType = local.row.reportType;

				if (not structKeyExists(local.datasets, local.reportType)) {
					local.datasets[local.reportType] = arrayNew(1);
				}

				// Build row data based on available columns
				local.rowData = arrayNew(1);
				arrayAppend(local.rowData, local.row.col1);
				if (not isNull(local.row.col2)) {
					arrayAppend(local.rowData, local.row.col2);
				}
				if (not isNull(local.row.col3)) {
					arrayAppend(local.rowData, local.row.col3);
				}

				arrayAppend(local.datasets[local.reportType], local.rowData);
			}

			local.returnStruct = {
				"success": true,
				"datasets": local.datasets
			};
		</cfscript>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>
