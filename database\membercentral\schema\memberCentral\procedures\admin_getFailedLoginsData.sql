ALTER PROC dbo.admin_getFailedLoginsData
@dateFrom datetime,
@dateTo datetime,
@reportType varchar(50)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Validate date range
	IF @dateFrom IS NULL OR @dateTo IS NULL BEGIN
		RAISERROR('Date range is required', 16, 1);
		RETURN -1;
	END

	-- Ensure dateTo includes full day
	SET @dateTo = DATEADD(day, 1, CAST(@dateTo AS date));

	-- Top Usernames with Failed Logins (brute-force attacks)
	IF @reportType = 'topUsernames' BEGIN
		SELECT username, COUNT(*) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY username
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- Top Passwords Attempted (password spraying attacks)
	IF @reportType = 'topPasswords' BEGIN
		SELECT password, COUNT(DISTINCT username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY password
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- Failed Login Attempts Over Time (spikes in attack activity)
	IF @reportType = 'attemptsOverTime' BEGIN
		SELECT CAST(dateentered AS DATE) AS FailureDate, 
			   DATEPART(hour, dateentered) AS FailureHour, 
			   COUNT(*) AS TotalFailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY CAST(dateentered AS DATE), DATEPART(hour, dateentered)
		ORDER BY FailureDate, FailureHour;
		RETURN 0;
	END

	-- Failed Logins by Site (site is being targeted)
	IF @reportType = 'failedBySite' BEGIN
		SELECT s.siteCode, COUNT(*) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed as lf
		INNER JOIN membercentral.dbo.sites as s on s.siteID = lf.siteID
		WHERE lf.dateentered >= @dateFrom AND lf.dateentered < @dateTo
		GROUP BY s.siteCode
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- Unique User-Password Combination Failures (credential stuffing)
	IF @reportType = 'uniqueCombinations' BEGIN
		SELECT COUNT(*) as TotalFailedAttempts, COUNT(DISTINCT username) as UniqueUsernames
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo;
		RETURN 0;
	END

	-- Top Attacking IP Addresses
	IF @reportType = 'topIPs' BEGIN
		SELECT ss.ipAddress, COUNT(fl.statsSessionID) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- IPs Attacking Multiple User Accounts (credential stuffing or brute-force attacks)
	IF @reportType = 'ipsMultipleAccounts' BEGIN
		SELECT ss.ipAddress, COUNT(DISTINCT fl.username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		HAVING COUNT(DISTINCT fl.username) > 10 -- Filter for IPs that tried at least 10 different usernames
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- User Account Attacked from Multiple IPs (distributed brute-force attack)
	IF @reportType = 'usersMultipleIPs' BEGIN
		SELECT fl.username, COUNT(DISTINCT ss.ipAddress) AS UniqueIPs
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY fl.username
		HAVING COUNT(DISTINCT ss.ipAddress) > 5 -- Filter for users attacked from more than 5 IPs
		ORDER BY UniqueIPs DESC;
		RETURN 0;
	END

	-- Top User-Agents for Failed Logins
	IF @reportType = 'topUserAgents' BEGIN
		SELECT ua.userAgent, COUNT(fl.statsSessionID) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- User-Agents Attacking Multiple Accounts (credential stuffing)
	IF @reportType = 'userAgentsMultipleAccounts' BEGIN
		SELECT ua.userAgent, COUNT(DISTINCT fl.username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT fl.username) > 10 -- Filter for UAs that tried at least 10 different usernames
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- Anomalous User-Agent to IP Address Ratio
	IF @reportType = 'anomalousUserAgents' BEGIN
		SELECT ua.userAgent, COUNT(fl.statsSessionID) AS TotalFailedAttempts, COUNT(DISTINCT ss.ipAddress) AS UniqueIPCount
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT ss.ipAddress) <= 2 -- For example, used by only 1 or 2 IPs
		ORDER BY TotalFailedAttempts DESC;
		RETURN 0;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
