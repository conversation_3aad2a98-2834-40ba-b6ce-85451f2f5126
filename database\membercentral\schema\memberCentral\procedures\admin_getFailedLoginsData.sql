CREATE PROC dbo.admin_getFailedLoginsData
@dateFrom datetime,
@dateTo datetime,
@reportType varchar(50) = 'all'

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Validate date range
	IF @dateFrom IS NULL OR @dateTo IS NULL BEGIN
		RAISERROR('Date range is required', 16, 1);
		RETURN -1;
	END

	-- Ensure dateTo includes full day
	SET @dateTo = DATEADD(day, 1, CAST(@dateTo AS date));

	-- Return all datasets when reportType = 'all'
	IF @reportType = 'all' BEGIN
		-- Dataset 1: Top Usernames with Failed Logins (brute-force attacks)
		SELECT 'topUsernames' AS reportType, username AS col1, CAST(COUNT(*) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY username
		ORDER BY COUNT(*) DESC;

		-- Dataset 2: Top Passwords Attempted (password spraying attacks)
		SELECT 'topPasswords' AS reportType, password AS col1, CAST(COUNT(DISTINCT username) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY password
		ORDER BY COUNT(DISTINCT username) DESC;

		-- Dataset 3: Failed Login Attempts Over Time (spikes in attack activity)
		SELECT 'attemptsOverTime' AS reportType, CAST(CAST(dateentered AS DATE) AS varchar(50)) AS col1,
			   CAST(DATEPART(hour, dateentered) AS varchar(50)) AS col2, CAST(COUNT(*) AS varchar(50)) AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY CAST(dateentered AS DATE), DATEPART(hour, dateentered)
		ORDER BY CAST(dateentered AS DATE), DATEPART(hour, dateentered);

		-- Dataset 4: Failed Logins by Site (site is being targeted)
		SELECT 'failedBySite' AS reportType, s.siteCode AS col1, CAST(COUNT(*) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed as lf
		INNER JOIN membercentral.dbo.sites as s on s.siteID = lf.siteID
		WHERE lf.dateentered >= @dateFrom AND lf.dateentered < @dateTo
		GROUP BY s.siteCode
		ORDER BY COUNT(*) DESC;

		-- Dataset 5: Unique User-Password Combination Failures (credential stuffing)
		SELECT 'uniqueCombinations' AS reportType, CAST(COUNT(*) AS varchar(50)) AS col1, CAST(COUNT(DISTINCT username) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo;

		-- Dataset 6: Top Attacking IP Addresses
		SELECT 'topIPs' AS reportType, ss.ipAddress AS col1, CAST(COUNT(fl.statsSessionID) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		ORDER BY COUNT(fl.statsSessionID) DESC;

		-- Dataset 7: IPs Attacking Multiple User Accounts (credential stuffing or brute-force attacks)
		SELECT 'ipsMultipleAccounts' AS reportType, ss.ipAddress AS col1, CAST(COUNT(DISTINCT fl.username) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		HAVING COUNT(DISTINCT fl.username) > 10
		ORDER BY COUNT(DISTINCT fl.username) DESC;

		-- Dataset 8: User Account Attacked from Multiple IPs (distributed brute-force attack)
		SELECT 'usersMultipleIPs' AS reportType, fl.username AS col1, CAST(COUNT(DISTINCT ss.ipAddress) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY fl.username
		HAVING COUNT(DISTINCT ss.ipAddress) > 5
		ORDER BY COUNT(DISTINCT ss.ipAddress) DESC;

		-- Dataset 9: Top User-Agents for Failed Logins
		SELECT 'topUserAgents' AS reportType, ISNULL(ua.userAgent, 'Unknown') AS col1, CAST(COUNT(fl.statsSessionID) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		ORDER BY COUNT(fl.statsSessionID) DESC;

		-- Dataset 10: User-Agents Attacking Multiple Accounts (credential stuffing)
		SELECT 'userAgentsMultipleAccounts' AS reportType, ISNULL(ua.userAgent, 'Unknown') AS col1, CAST(COUNT(DISTINCT fl.username) AS varchar(50)) AS col2, NULL AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT fl.username) > 10
		ORDER BY COUNT(DISTINCT fl.username) DESC;

		-- Dataset 11: Anomalous User-Agent to IP Address Ratio
		SELECT 'anomalousUserAgents' AS reportType, ISNULL(ua.userAgent, 'Unknown') AS col1, CAST(COUNT(fl.statsSessionID) AS varchar(50)) AS col2, CAST(COUNT(DISTINCT ss.ipAddress) AS varchar(50)) AS col3
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT ss.ipAddress) <= 2
		ORDER BY COUNT(fl.statsSessionID) DESC;

		RETURN 0;
	END

	-- Individual report types (for backward compatibility)
	-- Top Usernames with Failed Logins (brute-force attacks)
	IF @reportType = 'topUsernames' BEGIN
		SELECT username, COUNT(*) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY username
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- Top Passwords Attempted (password spraying attacks)
	IF @reportType = 'topPasswords' BEGIN
		SELECT password, COUNT(DISTINCT username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY password
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- Failed Login Attempts Over Time (spikes in attack activity)
	IF @reportType = 'attemptsOverTime' BEGIN
		SELECT CAST(dateentered AS DATE) AS FailureDate, 
			   DATEPART(hour, dateentered) AS FailureHour, 
			   COUNT(*) AS TotalFailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo
		GROUP BY CAST(dateentered AS DATE), DATEPART(hour, dateentered)
		ORDER BY FailureDate, FailureHour;
		RETURN 0;
	END

	-- Failed Logins by Site (site is being targeted)
	IF @reportType = 'failedBySite' BEGIN
		SELECT s.siteCode, COUNT(*) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed as lf
		INNER JOIN membercentral.dbo.sites as s on s.siteID = lf.siteID
		WHERE lf.dateentered >= @dateFrom AND lf.dateentered < @dateTo
		GROUP BY s.siteCode
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- Unique User-Password Combination Failures (credential stuffing)
	IF @reportType = 'uniqueCombinations' BEGIN
		SELECT COUNT(*) as TotalFailedAttempts, COUNT(DISTINCT username) as UniqueUsernames
		FROM platformStatsMC.dbo.ams_memberLoginsFailed
		WHERE dateentered >= @dateFrom AND dateentered < @dateTo;
		RETURN 0;
	END

	-- Top Attacking IP Addresses
	IF @reportType = 'topIPs' BEGIN
		SELECT ss.ipAddress, COUNT(fl.statsSessionID) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- IPs Attacking Multiple User Accounts (credential stuffing or brute-force attacks)
	IF @reportType = 'ipsMultipleAccounts' BEGIN
		SELECT ss.ipAddress, COUNT(DISTINCT fl.username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ss.ipAddress
		HAVING COUNT(DISTINCT fl.username) > 10 -- Filter for IPs that tried at least 10 different usernames
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- User Account Attacked from Multiple IPs (distributed brute-force attack)
	IF @reportType = 'usersMultipleIPs' BEGIN
		SELECT fl.username, COUNT(DISTINCT ss.ipAddress) AS UniqueIPs
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY fl.username
		HAVING COUNT(DISTINCT ss.ipAddress) > 5 -- Filter for users attacked from more than 5 IPs
		ORDER BY UniqueIPs DESC;
		RETURN 0;
	END

	-- Top User-Agents for Failed Logins
	IF @reportType = 'topUserAgents' BEGIN
		SELECT ua.userAgent, COUNT(fl.statsSessionID) AS FailedAttempts
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		ORDER BY FailedAttempts DESC;
		RETURN 0;
	END

	-- User-Agents Attacking Multiple Accounts (credential stuffing)
	IF @reportType = 'userAgentsMultipleAccounts' BEGIN
		SELECT ua.userAgent, COUNT(DISTINCT fl.username) AS UniqueUsernamesAttempted
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT fl.username) > 10 -- Filter for UAs that tried at least 10 different usernames
		ORDER BY UniqueUsernamesAttempted DESC;
		RETURN 0;
	END

	-- Anomalous User-Agent to IP Address Ratio
	IF @reportType = 'anomalousUserAgents' BEGIN
		SELECT ua.userAgent, COUNT(fl.statsSessionID) AS TotalFailedAttempts, COUNT(DISTINCT ss.ipAddress) AS UniqueIPCount
		FROM platformStatsMC.dbo.ams_memberLoginsFailed AS fl
		INNER JOIN platformStatsMC.dbo.statsSessions AS ss ON fl.statsSessionID = ss.sessionID
		LEFT OUTER JOIN platformStatsMC.dbo.statsUserAgents as ua on ua.useragentID = ss.userAgentID
		WHERE fl.dateentered >= @dateFrom AND fl.dateentered < @dateTo
		GROUP BY ua.userAgent
		HAVING COUNT(DISTINCT ss.ipAddress) <= 2 -- For example, used by only 1 or 2 IPs
		ORDER BY TotalFailedAttempts DESC;
		RETURN 0;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
